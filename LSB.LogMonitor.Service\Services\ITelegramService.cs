﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using LSB.LogMonitor.Service.Models;

namespace LSB.LogMonitor.Services
{
    public interface ITelegramService
    {
        Task SendLogNotificationAsync(List<ClientLogInfo> clientsWithLogs, DateTime date, CancellationToken cancellationToken = default);
        Task SendTestMessageAsync(CancellationToken cancellationToken = default);

        Task SendDailyBusinessMetricsAlert(List<ClientLogInfo> clientsWithIssues, DateTime date, CancellationToken cancellationToken = default);
        Task SendDailyBusinessMetricsHealthySummary(List<ClientLogInfo> healthyClients, DateTime date, CancellationToken cancellationToken = default);
    }
}
