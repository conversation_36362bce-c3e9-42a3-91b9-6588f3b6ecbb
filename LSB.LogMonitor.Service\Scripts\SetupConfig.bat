@echo off
echo ========================================
echo LSB Telemetry Service - SETUP CONFIG
echo ========================================

echo.
echo This script will help you configure the service safely.
echo.

REM Check if template exists
if not exist "appsettings.template.json" (
    echo ERROR: appsettings.template.json not found!
    echo Please make sure the template file exists.
    pause
    exit /b 1
)

echo Found template file - OK
echo.

REM Get user input
echo Please provide the following information:
echo.

set /p BOT_TOKEN="Enter your Telegram Bot Token: "
if "%BOT_TOKEN%"=="" (
    echo ERROR: Bot Token cannot be empty!
    pause
    exit /b 1
)

set /p MAIN_CHAT_ID="Enter your Main Chat ID: "
if "%MAIN_CHAT_ID%"=="" (
    echo ERROR: Main Chat ID cannot be empty!
    pause
    exit /b 1
)

set /p CLIENT1_NAME="Enter Client 1 Name (or press Enter to skip): "
if not "%CLIENT1_NAME%"=="" (
    set /p CLIENT1_CHAT_ID="Enter Client 1 Chat ID: "
)

set /p CLIENT2_NAME="Enter Client 2 Name (or press Enter to skip): "
if not "%CLIENT2_NAME%"=="" (
    set /p CLIENT2_CHAT_ID="Enter Client 2 Chat ID: "
)

set /p LOG_PATH="Enter Log Root Path (or press Enter for default): "
if "%LOG_PATH%"=="" (
    set LOG_PATH=C:\\ProgramData\\DaBox\\LSBHub\\Logs
)

echo.
echo ========================================
echo CONFIGURATION SUMMARY:
echo ========================================
echo Bot Token: %BOT_TOKEN%
echo Main Chat ID: %MAIN_CHAT_ID%
if not "%CLIENT1_NAME%"=="" echo Client 1: %CLIENT1_NAME% (%CLIENT1_CHAT_ID%)
if not "%CLIENT2_NAME%"=="" echo Client 2: %CLIENT2_NAME% (%CLIENT2_CHAT_ID%)
echo Log Path: %LOG_PATH%
echo ========================================
echo.

set /p CONFIRM="Is this information correct? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Configuration cancelled.
    pause
    exit /b 0
)

echo.
echo Creating appsettings.json...

REM Create the config file using PowerShell
powershell -Command ^
"$template = Get-Content 'appsettings.template.json' -Raw; ^
$config = $template -replace 'PASTE_YOUR_BOT_TOKEN_HERE', '%BOT_TOKEN%'; ^
$config = $config -replace 'PASTE_YOUR_MAIN_CHAT_ID_HERE', '%MAIN_CHAT_ID%'; ^
if ('%CLIENT1_NAME%' -ne '') { ^
    $config = $config -replace 'ClientName1', '%CLIENT1_NAME%'; ^
    $config = $config -replace 'PASTE_CLIENT_1_CHAT_ID_HERE', '%CLIENT1_CHAT_ID%'; ^
} ^
if ('%CLIENT2_NAME%' -ne '') { ^
    $config = $config -replace 'ClientName2', '%CLIENT2_NAME%'; ^
    $config = $config -replace 'PASTE_CLIENT_2_CHAT_ID_HERE', '%CLIENT2_CHAT_ID%'; ^
} ^
$config = $config -replace 'C:\\\\ProgramData\\\\DaBox\\\\LSBHub\\\\Logs', '%LOG_PATH%'; ^
$config | Out-File 'appsettings.json' -Encoding UTF8"

if %errorLevel% equ 0 (
    echo ✓ Configuration file created successfully!
    echo.
    echo File: appsettings.json
    echo.
    echo ⚠️  SECURITY WARNING:
    echo The appsettings.json file now contains sensitive information.
    echo - Do NOT share this file with anyone
    echo - Do NOT commit this file to version control
    echo - Keep this file secure on the server only
    echo.
    echo Next step: Run Scripts\StartService.bat to install the service
) else (
    echo ✗ Failed to create configuration file
    echo Please check the template file and try again.
)

echo.
pause
