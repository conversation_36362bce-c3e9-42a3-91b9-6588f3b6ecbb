﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using LSB.LogMonitor.Service.Models;
using LSB.LogMonitor.Service.Contracts;

namespace LSB.LogMonitor.Services
{
    public class LogMonitorWorker : BackgroundService
    {
        private readonly ILogger<LogMonitorWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly ILogService _logService;
        private readonly ITelegramService _telegramService;
        private readonly IConfigService _configService;
        private readonly TimeSpan _checkInterval;

        public LogMonitorWorker(
            ILogger<LogMonitorWorker> logger,
            IConfiguration configuration,
            ILogService logService,
            ITelegramService telegramService,
            IConfigService configService)
        {
            _logger = logger;
            _configuration = configuration;
            _logService = logService;
            _telegramService = telegramService;
            _configService = configService;

            // Get interval from config (default 1 hour)
            var intervalHours = _configuration.GetValue<double>("LogMonitor:CheckIntervalHours", 1.0);
            _checkInterval = TimeSpan.FromHours(intervalHours);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("LSB Log Monitor Service started at: {time}", DateTimeOffset.Now);
            _logger.LogInformation("Check interval: {interval} hours", _checkInterval.TotalHours);

            // Comment out initial run to avoid double messages
            // try
            // {
            //     _logger.LogInformation("Running initial log check...");
            //     await CheckLogsForAllClients(stoppingToken);
            //     _logger.LogInformation("Initial log check completed");
            // }
            // catch (Exception ex)
            // {
            //     _logger.LogError(ex, "Error occurred during initial log check");
            // }

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckLogsForAllClients(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while checking logs");
                }

                // Wait for next check interval
                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        private async Task CheckLogsForAllClients(CancellationToken cancellationToken)
        {
            // Get AccName from config instead of Environment.MachineName
            var accName = await _configService.GetAccNameAsync();
            _logger.LogInformation("Starting log check for {AccName}", accName);

            // Get available clients using existing method
            var clientNames = await _logService.GetAvailableClientNamesAsync(cancellationToken);

            if (!clientNames.Any())
            {
                _logger.LogWarning("No clients found for {AccName}", accName);
                return;
            }

            var today = DateTime.Today; // Only check today's logs
            var clientsWithLogs = new List<ClientLogInfo>();

            // Check each client for today's logs
            foreach (var clientName in clientNames)
            {
                try
                {
                    // Use existing method to check if logs exist
                    var logsExist = await _logService.LogsExistAsync(today, clientName, cancellationToken);

                    if (logsExist)
                    {
                        // Use existing method to generate summary
                        var summary = await _logService.GenerateLogSummaryAsync(today, clientName, cancellationToken);

                        var errorCount = summary.HealthSummary?.TotalErrors ?? 0;
                        if (errorCount > 0)
                        {
                            _logger.LogInformation("{ClientName}: {ErrorCount} errors found", clientName, errorCount);
                        }

                        clientsWithLogs.Add(new ClientLogInfo
                        {
                            ClientName = clientName,
                            LogFileCount = summary.LogFiles.Count,
                            Summary = summary,
                            Date = today
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing client {ClientName} on account: {AccName}", clientName, accName);
                    // Continue with other clients even if one fails
                }
            }

            // Send Telegram notification if any clients have logs
            if (clientsWithLogs.Any())
            {
                await _telegramService.SendLogNotificationAsync(clientsWithLogs, today, cancellationToken);
                _logger.LogInformation("Notification sent for {AccName}", accName);
            }
            else
            {
                _logger.LogInformation("No logs found for any clients on account: {AccName} on {Date}", accName, today.ToString("yyyy-MM-dd"));
            }

            _logger.LogInformation("Log check completed on account: {AccName} at: {time}", accName, DateTimeOffset.Now);
        }
    }
}
