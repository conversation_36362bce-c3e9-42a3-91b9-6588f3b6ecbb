# LSB Telemetry Service - Deployment Package Creator
# This script creates a ZIP package for deploying the service to other machines

param(
    [string]$OutputPath = ".\LSB.Telemetry.Service.Deployment.zip",
    [switch]$Force
)

# Set execution policy for current session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "LSB Telemetry Service - Package Creator" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Define paths
$ProjectRoot = Get-Location
$BinPath = Join-Path $ProjectRoot "LSB.LogMonitor.Service\bin\Release\net8.0"
$ScriptsPath = Join-Path $ProjectRoot "LSB.LogMonitor.Service\Scripts"
$TempPackageDir = Join-Path $ProjectRoot "TempPackage"

# Check if Release build exists
if (-not (Test-Path $BinPath)) {
    Write-Host "ERROR: Release build not found at: $BinPath" -ForegroundColor Red
    Write-Host "Please build the project in Release mode first." -ForegroundColor Red
    exit 1
}

# Check if Scripts directory exists
if (-not (Test-Path $ScriptsPath)) {
    Write-Host "ERROR: Scripts directory not found at: $ScriptsPath" -ForegroundColor Red
    exit 1
}

# Remove existing package if Force is specified
if ($Force -and (Test-Path $OutputPath)) {
    Write-Host "Removing existing package: $OutputPath" -ForegroundColor Yellow
    Remove-Item $OutputPath -Force
}

# Check if output file already exists
if (Test-Path $OutputPath) {
    Write-Host "ERROR: Output file already exists: $OutputPath" -ForegroundColor Red
    Write-Host "Use -Force parameter to overwrite or specify a different path." -ForegroundColor Red
    exit 1
}

# Create temporary package directory
Write-Host "Creating temporary package directory..." -ForegroundColor Green
if (Test-Path $TempPackageDir) {
    Remove-Item $TempPackageDir -Recurse -Force
}
New-Item -ItemType Directory -Path $TempPackageDir -Force | Out-Null

try {
    # Copy main executable and dependencies
    Write-Host "Copying application files..." -ForegroundColor Green
    $AppDir = Join-Path $TempPackageDir "App"
    New-Item -ItemType Directory -Path $AppDir -Force | Out-Null
    
    # Copy all files from Release build (excluding PDB files for smaller package)
    Get-ChildItem -Path $BinPath -Recurse | Where-Object { 
        -not $_.PSIsContainer -and $_.Extension -ne ".pdb" 
    } | ForEach-Object {
        $relativePath = $_.FullName.Substring($BinPath.Length + 1)
        $destPath = Join-Path $AppDir $relativePath
        $destDir = Split-Path $destPath -Parent
        
        if (-not (Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        
        Copy-Item $_.FullName $destPath -Force
        Write-Host "  Copied: $relativePath" -ForegroundColor Gray
    }
    
    # Copy utility scripts
    Write-Host "Copying utility scripts..." -ForegroundColor Green
    $ScriptsDir = Join-Path $TempPackageDir "Scripts"
    New-Item -ItemType Directory -Path $ScriptsDir -Force | Out-Null
    
    $scriptFiles = @("StartService.bat", "StopService.bat", "UninstallService.bat")
    foreach ($scriptFile in $scriptFiles) {
        $sourcePath = Join-Path $ScriptsPath $scriptFile
        if (Test-Path $sourcePath) {
            Copy-Item $sourcePath $ScriptsDir -Force
            Write-Host "  Copied: $scriptFile" -ForegroundColor Gray
        } else {
            Write-Host "  WARNING: Script not found: $scriptFile" -ForegroundColor Yellow
        }
    }
    
    # Create README for deployment
    Write-Host "Creating deployment README..." -ForegroundColor Green
    $readmeContent = @"
# LSB Telemetry Service - Deployment Package

## System Requirements
- Windows 10/11 or Windows Server 2016+
- .NET 8.0 Runtime (will be installed automatically if missing)
- Administrator privileges for service installation

## Installation Instructions

1. Extract this ZIP package to a folder on the target machine
   Example: C:\Program Files\LSB\TelemetryService\

2. Open Command Prompt or PowerShell as Administrator

3. Navigate to the Scripts folder:
   cd "C:\Program Files\LSB\TelemetryService\Scripts"

4. Run the installation script:
   StartService.bat

## Service Management

### Start/Install Service
- Run: Scripts\StartService.bat
- This script will:
  - Check if service is already installed
  - Install the service if not present
  - Start the service
  - Configure automatic startup

### Stop Service
- Run: Scripts\StopService.bat
- This will stop the running service

### Uninstall Service
- Run: Scripts\UninstallService.bat
- This will stop and completely remove the service

## Configuration

The service uses configuration files located in:
- App\appsettings.json (main configuration)
- C:\ProgramData\LSB\logmonitor.config (client-specific settings)

Make sure to configure these files according to your environment before starting the service.

## Troubleshooting

1. If service fails to start:
   - Check Windows Event Viewer (Windows Logs > Application)
   - Verify configuration files are valid JSON
   - Ensure all required directories exist

2. If installation fails:
   - Verify you're running as Administrator
   - Check if service name conflicts with existing services
   - Ensure .NET 8.0 Runtime is installed

3. For support:
   - Check service logs in Windows Event Viewer
   - Review configuration files for syntax errors

## Package Contents

- App\                     - Main application files
- Scripts\                 - Service management scripts
- README.txt              - This file

Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
    
    $readmeContent | Out-File -FilePath (Join-Path $TempPackageDir "README.txt") -Encoding UTF8
    
    # Create the ZIP package
    Write-Host "Creating ZIP package..." -ForegroundColor Green
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory($TempPackageDir, $OutputPath)
    
    # Get package size
    $packageSize = (Get-Item $OutputPath).Length
    $packageSizeMB = [math]::Round($packageSize / 1MB, 2)
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "PACKAGE CREATED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Package: $OutputPath" -ForegroundColor White
    Write-Host "Size: $packageSizeMB MB" -ForegroundColor White
    Write-Host ""
    Write-Host "The package contains:" -ForegroundColor Cyan
    Write-Host "- Application executable and dependencies" -ForegroundColor White
    Write-Host "- Service management scripts (Start/Stop/Uninstall)" -ForegroundColor White
    Write-Host "- Deployment instructions (README.txt)" -ForegroundColor White
    Write-Host ""
    Write-Host "Ready for deployment to other machines!" -ForegroundColor Green
    
} catch {
    Write-Host "ERROR: Failed to create package: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Clean up temporary directory
    if (Test-Path $TempPackageDir) {
        Write-Host "Cleaning up temporary files..." -ForegroundColor Gray
        Remove-Item $TempPackageDir -Recurse -Force
    }
}
