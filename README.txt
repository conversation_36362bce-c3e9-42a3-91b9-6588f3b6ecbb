LSB Telemetry Service - Console Application
==========================================

Installation Instructions:
1. Extract all files to desired directory (e.g., C:\LSB\TelemetryService\)
2. Copy appsettings.template.json to appsettings.json
3. Edit appsettings.json with your Telegram bot token and channel IDs
4. Create config file at C:\ProgramData\LSB\logmonitor.config
5. Run SetupConfig.bat to create initial configuration
6. Run StartService.bat to install and start the service

Utility Scripts:
- StartService.bat: Check/Install/Start the service
- StopService.bat: Stop the service
- UninstallService.bat: Uninstall the service
- SetupConfig.bat: Setup initial configuration

Configuration Files:
- appsettings.json: Service configuration (Telegram settings)
- C:\ProgramData\LSB\logmonitor.config: Client configuration (AccName, LogPath)

Example logmonitor.config:
{
  "AccName": "YourClientName",
  "LogPath": "C:\\ProgramData\\DaBox\\LSBHub\\Logs"
}

Requirements:
- .NET 8.0 Runtime
- Windows 10/11 or Windows Server 2016+
- Administrator privileges for service installation

Support: LSB 
