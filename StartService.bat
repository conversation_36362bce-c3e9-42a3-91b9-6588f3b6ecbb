@echo off
echo ========================================
echo LSB Telemetry Service - START SERVICE
echo ========================================

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - OK
echo.

REM Set service configuration
set SERVICE_NAME=LSBTelemetryService
set SERVICE_DISPLAY_NAME=LSB Telemetry Service
set SERVICE_DESCRIPTION=LSB Telemetry Service - System monitoring and error notifications

REM Get current directory and executable path
set CURRENT_DIR=%~dp0
set PARENT_DIR=%CURRENT_DIR%..
set EXE_PATH=%PARENT_DIR%\LSB.LogMonitor.Service.exe

REM Convert to absolute path
for %%i in ("%EXE_PATH%") do set FULL_PATH=%%~fi

echo Service Name: %SERVICE_NAME%
echo Display Name: %SERVICE_DISPLAY_NAME%
echo Executable Path: %FULL_PATH%
echo.

REM Check if executable exists
if not exist "%FULL_PATH%" (
    echo ERROR: Service executable not found at: %FULL_PATH%
    echo Please make sure the application is built and the executable exists.
    pause
    exit /b 1
)

echo Executable found - OK
echo.

REM Check if service already exists
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% EQU 0 (
    echo Service already exists. Checking status...
    
    REM Check if service is running
    sc query "%SERVICE_NAME%" | find "RUNNING" >nul
    if %errorLevel% EQU 0 (
        echo Service is already running!
        echo.
        sc query "%SERVICE_NAME%"
        echo.
        echo Service is ready to use.
        pause
        exit /b 0
    ) else (
        echo Service exists but not running. Starting service...
        sc start "%SERVICE_NAME%"
        if %errorLevel% EQU 0 (
            echo ✓ Service started successfully!
            echo.
            sc query "%SERVICE_NAME%"
        ) else (
            echo ✗ Failed to start service. Error code: %errorLevel%
            echo Check Windows Event Viewer for details.
        )
        pause
        exit /b %errorLevel%
    )
)

echo Service does not exist. Installing new service...
echo.

REM Install the service
echo Installing service with command:
echo sc create "%SERVICE_NAME%" binPath= "%FULL_PATH%" start= auto DisplayName= "%SERVICE_DISPLAY_NAME%" type= own
echo.

sc create "%SERVICE_NAME%" binPath= "%FULL_PATH%" start= auto DisplayName= "%SERVICE_DISPLAY_NAME%" type= own

if %errorLevel% EQU 0 (
    echo ✓ Service installed successfully!
    echo.
    
    REM Set service description
    sc description "%SERVICE_NAME%" "%SERVICE_DESCRIPTION%"
    
    REM Configure service failure actions (restart on failure)
    sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/30000/restart/60000/restart/60000
    
    echo Verifying service configuration...
    sc qc "%SERVICE_NAME%"
    echo.
    
    echo Starting service...
    sc start "%SERVICE_NAME%"
    
    if %errorLevel% EQU 0 (
        echo ✓ Service started successfully!
        echo.
        echo Checking service status...
        timeout /t 3 /nobreak >nul
        sc query "%SERVICE_NAME%"
        echo.
        echo ========================================
        echo SERVICE INSTALLATION COMPLETED!
        echo ========================================
        echo.
        echo The LSB Telemetry Service is now running.
        echo It will automatically start when Windows boots.
        echo.
        echo To stop the service, run: StopService.bat
        echo To uninstall the service, run: UninstallService.bat
        echo.
    ) else (
        echo ✗ Service installed but failed to start. Error code: %errorLevel%
        echo Check Windows Event Viewer for details.
        echo You can try starting it manually from Services.msc
    )
) else (
    echo ✗ Failed to install service. Error code: %errorLevel%
    echo.
    echo Common causes:
    echo - Service name already exists
    echo - Insufficient permissions
    echo - Invalid executable path
    echo.
    echo Please check the error and try again.
)

echo.
pause
