﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LSB.LogMonitor.Service.Contracts;
using LSB.LogMonitor.Service.Models;
using LSB.LogMonitor.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace LSB.LogMonitor.Service.Services
{
    public class DailyBusinessMetricsCheckService : BackgroundService
    {
        private readonly ILogger<DailyBusinessMetricsCheckService> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly Timer _dailyTimer;

        public DailyBusinessMetricsCheckService(
            ILogger<DailyBusinessMetricsCheckService> logger,
            IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;

            // Schedule daily check at 10:00 UTC
            _dailyTimer = new Timer(async _ => await PerformDailyCheck(), null,
                GetTimeUntilNextRun(), TimeSpan.FromDays(1));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Daily business metrics check service started. Next check at 10:00 UTC.");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Wait for the next day
                    await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }

            _logger.LogInformation("Daily business metrics check service stopped.");
        }

        private TimeSpan GetTimeUntilNextRun()
        {
            var now = DateTime.UtcNow;
            var nextRun = DateTime.Today.AddHours(10); // 10:00 UTC today

            // If it's already past 10:00 UTC today, schedule for tomorrow
            if (now >= nextRun)
            {
                nextRun = nextRun.AddDays(1);
            }

            var delay = nextRun - now;
            _logger.LogInformation("Next daily business metrics check scheduled for {NextRun} UTC (in {Delay})",
                nextRun, delay);

            return delay;
        }

        private async Task PerformDailyCheck()
        {
            _logger.LogInformation("Starting daily business metrics check at {Time} UTC", DateTime.UtcNow);

            try
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var logService = scope.ServiceProvider.GetRequiredService<ILogService>();
                var telegramService = scope.ServiceProvider.GetRequiredService<ITelegramService>();

                // Get yesterday's date for checking
                var checkDate = DateTime.Today.AddDays(-1);

                // Get all available clients
                var clientNames = await logService.GetAvailableClientNamesAsync();

                if (!clientNames.Any())
                {
                    _logger.LogWarning("No clients found for daily business metrics check");
                    return;
                }

                var result = new DailyBusinessMetricsResult
                {
                    CheckDate = checkDate,
                    CheckTime = DateTime.UtcNow
                };

                // Check each client's business metrics
                foreach (var clientName in clientNames)
                {
                    try
                    {
                        var summary = await logService.GenerateLogSummaryAsync(checkDate, clientName);
                        var clientMetrics = AnalyzeClientBusinessMetrics(clientName, summary);
                        result.ClientMetrics.Add(clientMetrics);

                        if (!clientMetrics.IsHealthy)
                        {
                            result.HasCriticalIssues = true;
                            result.CriticalIssues.AddRange(clientMetrics.Issues.Select(issue => $"{clientName}: {issue}"));
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error checking business metrics for client {ClientName}", clientName);

                        result.HasCriticalIssues = true;
                        result.CriticalIssues.Add($"{clientName}: Error retrieving metrics - {ex.Message}");
                    }
                }

                // Send notification if there are critical issues
                if (result.HasCriticalIssues)
                {
                    await SendBusinessMetricsAlert(result, telegramService);
                }
                else
                {
                    _logger.LogInformation("Daily business metrics check completed - all clients healthy");
                    // Optionally send a "all good" summary
                    await SendBusinessMetricsHealthySummary(result, telegramService);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during daily business metrics check");
            }
        }

        private ClientBusinessMetrics AnalyzeClientBusinessMetrics(string clientName, LogSummaryResponse summary)
        {
            var metrics = new ClientBusinessMetrics
            {
                ClientName = clientName,
                LastDataReceived = DateTime.UtcNow // This should be populated from actual data
            };

            // Extract business metrics from health summary
            if (summary.HealthSummary != null)
            {
                metrics.KeywordReports = summary.HealthSummary.TotalKeywordReports;
                metrics.SqsConversions = summary.HealthSummary.TotalSqsConversions;
                metrics.SqsTraffics = summary.HealthSummary.TotalSqsTraffics;

                // Check for issues (any of the 3 metrics is 0)
                if (metrics.KeywordReports == 0)
                {
                    metrics.Issues.Add("❌ **Keyword Reports**: 0 reports received");
                }

                if (metrics.SqsConversions == 0)
                {
                    metrics.Issues.Add("❌ **SQS Conversions**: 0 conversions received");
                }

                if (metrics.SqsTraffics == 0)
                {
                    metrics.Issues.Add("❌ **SQS Traffics**: 0 traffic data received");
                }

                metrics.IsHealthy = !metrics.Issues.Any();
            }
            else
            {
                // No health summary available
                metrics.Issues.Add("❌ **No health data available**");
                metrics.IsHealthy = false;
            }

            return metrics;
        }

        // Cập nhật method SendBusinessMetricsAlert trong DailyBusinessMetricsCheckService:

        private async Task SendBusinessMetricsAlert(DailyBusinessMetricsResult result, ITelegramService telegramService)
        {
            _logger.LogWarning("Sending business metrics alert - {IssueCount} critical issues found",
                result.CriticalIssues.Count);

            // Create mock ClientLogInfo for telegram service
            var clientsWithIssues = result.ClientMetrics
                .Where(c => !c.IsHealthy)
                .Select(c => new ClientLogInfo
                {
                    ClientName = c.ClientName,
                    Summary = new LogSummaryResponse
                    {
                        ClientName = c.ClientName,
                        Date = result.CheckDate,
                        HealthSummary = new SystemHealthSummary
                        {
                            TotalKeywordReports = c.KeywordReports,
                            TotalSqsConversions = c.SqsConversions,
                            TotalSqsTraffics = c.SqsTraffics,
                            OverallStatus = "Critical",
                            TotalErrors = c.Issues.Count,
                            // FIX: Không assign trực tiếp, sẽ dùng method
                            MissingBusinessDataServices = c.Issues
                        }
                    }
                }).ToList();

            // FIX: Gọi method để calculate RequiresImmediateAttention cho mỗi client
            foreach (var client in clientsWithIssues)
            {
                client.Summary.HealthSummary?.CalculateRequiresImmediateAttention();
            }

            await telegramService.SendDailyBusinessMetricsAlert(clientsWithIssues, result.CheckDate);
        }

        private async Task SendBusinessMetricsHealthySummary(DailyBusinessMetricsResult result, ITelegramService telegramService)
        {
            _logger.LogInformation("All business metrics healthy - sending summary");

            var healthyClients = result.ClientMetrics.Select(c => new ClientLogInfo
            {
                ClientName = c.ClientName,
                Summary = new LogSummaryResponse
                {
                    ClientName = c.ClientName,
                    Date = result.CheckDate,
                    HealthSummary = new SystemHealthSummary
                    {
                        TotalKeywordReports = c.KeywordReports,
                        TotalSqsConversions = c.SqsConversions,
                        TotalSqsTraffics = c.SqsTraffics,
                        OverallStatus = "Healthy",
                        TotalErrors = 0
                    }
                }
            }).ToList();

            await telegramService.SendDailyBusinessMetricsHealthySummary(healthyClients, result.CheckDate);
        }

        public override void Dispose()
        {
            _dailyTimer?.Dispose();
            base.Dispose();
        }
    }

    // Extension for IServiceCollection to register the service
    public static class DailyBusinessMetricsServiceExtensions
    {
        public static IServiceCollection AddDailyBusinessMetricsCheck(this IServiceCollection services)
        {
            services.AddHostedService<DailyBusinessMetricsCheckService>();
            return services;
        }
    }
}
