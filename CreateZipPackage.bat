@echo off
echo ========================================
echo LSB Telemetry Service - ZIP Creator
echo ========================================
echo.

REM Set variables
set PACKAGE_NAME=LSB.Telemetry.Service.Deployment
set TEMP_DIR=TempPackage
set BIN_PATH=LSB.LogMonitor.Service\bin\Release\net8.0
set SCRIPTS_PATH=LSB.LogMonitor.Service\Scripts

REM Check if Release build exists
if not exist "%BIN_PATH%" (
    echo ERROR: Release build not found at: %BIN_PATH%
    echo Please build the project in Release mode first.
    pause
    exit /b 1
)

REM Clean up any existing files
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
if exist "%PACKAGE_NAME%.zip" del "%PACKAGE_NAME%.zip"

REM Create package structure
echo Creating package structure...
mkdir "%TEMP_DIR%"
mkdir "%TEMP_DIR%\App"
mkdir "%TEMP_DIR%\Scripts"

REM Copy application files
echo Copying application files...
xcopy "%BIN_PATH%\*" "%TEMP_DIR%\App\" /E /I /Y /Q
del "%TEMP_DIR%\App\*.pdb" 2>nul

REM Copy scripts
echo Copying utility scripts...
copy "%SCRIPTS_PATH%\StartService.bat" "%TEMP_DIR%\Scripts\" >nul
copy "%SCRIPTS_PATH%\StopService.bat" "%TEMP_DIR%\Scripts\" >nul
copy "%SCRIPTS_PATH%\UninstallService.bat" "%TEMP_DIR%\Scripts\" >nul

REM Create README
echo Creating README...
echo # LSB Telemetry Service - Deployment Package > "%TEMP_DIR%\README.txt"
echo. >> "%TEMP_DIR%\README.txt"
echo ## Quick Start >> "%TEMP_DIR%\README.txt"
echo 1. Extract this package to a permanent location >> "%TEMP_DIR%\README.txt"
echo 2. Run Scripts\StartService.bat as Administrator >> "%TEMP_DIR%\README.txt"
echo. >> "%TEMP_DIR%\README.txt"
echo ## Service Management >> "%TEMP_DIR%\README.txt"
echo - Start/Install: Scripts\StartService.bat >> "%TEMP_DIR%\README.txt"
echo - Stop: Scripts\StopService.bat >> "%TEMP_DIR%\README.txt"
echo - Uninstall: Scripts\UninstallService.bat >> "%TEMP_DIR%\README.txt"
echo. >> "%TEMP_DIR%\README.txt"
echo Generated: %DATE% %TIME% >> "%TEMP_DIR%\README.txt"

REM Create ZIP using PowerShell
echo Creating ZIP package using PowerShell...
powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if exist "%PACKAGE_NAME%.zip" (
    echo.
    echo ========================================
    echo SUCCESS! ZIP PACKAGE CREATED
    echo ========================================
    for %%A in ("%PACKAGE_NAME%.zip") do echo Package: %%~nxA (%%~zA bytes^)
    echo.
    echo Ready for deployment to Windows machines!
    echo.
) else (
    echo.
    echo ERROR: Failed to create ZIP package
    echo Package files are available in: %TEMP_DIR%
    echo.
)

REM Clean up
echo Cleaning up temporary files...
rmdir /s /q "%TEMP_DIR%"

echo.
pause
