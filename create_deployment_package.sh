#!/bin/bash

echo "========================================"
echo "LSB Telemetry Service - Package Creator"
echo "========================================"
echo ""

# Set variables
PACKAGE_NAME="LSB.Telemetry.Service.Deployment"
TEMP_DIR="TempPackage"
BIN_PATH="LSB.LogMonitor.Service/bin/Release/net8.0"
SCRIPTS_PATH="LSB.LogMonitor.Service/Scripts"

# Check if Release build exists
if [ ! -d "$BIN_PATH" ]; then
    echo "ERROR: Release build not found at: $BIN_PATH"
    echo "Please build the project in Release mode first."
    exit 1
fi

# Check if Scripts directory exists
if [ ! -d "$SCRIPTS_PATH" ]; then
    echo "ERROR: Scripts directory not found at: $SCRIPTS_PATH"
    exit 1
fi

# Clean up any existing temp directory
if [ -d "$TEMP_DIR" ]; then
    echo "Cleaning up existing temp directory..."
    rm -rf "$TEMP_DIR"
fi

# Create temporary package structure
echo "Creating package structure..."
mkdir -p "$TEMP_DIR/App"
mkdir -p "$TEMP_DIR/Scripts"

# Copy application files (excluding PDB files)
echo "Copying application files..."
cp -r "$BIN_PATH"/* "$TEMP_DIR/App/"
find "$TEMP_DIR/App" -name "*.pdb" -delete

# Copy utility scripts
echo "Copying utility scripts..."
cp "$SCRIPTS_PATH/StartService.bat" "$TEMP_DIR/Scripts/" 2>/dev/null || echo "Warning: StartService.bat not found"
cp "$SCRIPTS_PATH/StopService.bat" "$TEMP_DIR/Scripts/" 2>/dev/null || echo "Warning: StopService.bat not found"
cp "$SCRIPTS_PATH/UninstallService.bat" "$TEMP_DIR/Scripts/" 2>/dev/null || echo "Warning: UninstallService.bat not found"

# Create README file
echo "Creating deployment README..."
cat > "$TEMP_DIR/README.txt" << 'EOF'
# LSB Telemetry Service - Deployment Package

## System Requirements
- Windows 10/11 or Windows Server 2016+
- .NET 8.0 Runtime (will be installed automatically if missing)
- Administrator privileges for service installation

## Installation Instructions

1. Extract this ZIP package to a folder on the target machine
   Example: C:\Program Files\LSB\TelemetryService\

2. Open Command Prompt or PowerShell as Administrator

3. Navigate to the Scripts folder:
   cd "C:\Program Files\LSB\TelemetryService\Scripts"

4. Run the installation script:
   StartService.bat

## Service Management

### Start/Install Service
- Run: Scripts\StartService.bat
- This script will:
  - Check if service is already installed
  - Install the service if not present
  - Start the service
  - Configure automatic startup

### Stop Service
- Run: Scripts\StopService.bat
- This will stop the running service

### Uninstall Service
- Run: Scripts\UninstallService.bat
- This will stop and completely remove the service

## Configuration

The service uses configuration files located in:
- App\appsettings.json (main configuration)
- C:\ProgramData\LSB\logmonitor.config (client-specific settings)

Make sure to configure these files according to your environment before starting the service.

## Troubleshooting

1. If service fails to start:
   - Check Windows Event Viewer (Windows Logs > Application)
   - Verify configuration files are valid JSON
   - Ensure all required directories exist

2. If installation fails:
   - Verify you're running as Administrator
   - Check if service name conflicts with existing services
   - Ensure .NET 8.0 Runtime is installed

3. For support:
   - Check service logs in Windows Event Viewer
   - Review configuration files for syntax errors

## Package Contents

- App\                     - Main application files
- Scripts\                 - Service management scripts
- README.txt              - This file

Generated on: $(date)
EOF

# Create ZIP package
echo "Creating ZIP package..."
if command -v zip >/dev/null 2>&1; then
    # Remove existing package if it exists
    [ -f "${PACKAGE_NAME}.zip" ] && rm "${PACKAGE_NAME}.zip"
    
    # Create ZIP
    cd "$TEMP_DIR"
    zip -r "../${PACKAGE_NAME}.zip" . >/dev/null 2>&1
    cd ..
    
    if [ -f "${PACKAGE_NAME}.zip" ]; then
        PACKAGE_SIZE=$(du -h "${PACKAGE_NAME}.zip" | cut -f1)
        echo ""
        echo "========================================"
        echo "PACKAGE CREATED SUCCESSFULLY!"
        echo "========================================"
        echo "Package: ${PACKAGE_NAME}.zip"
        echo "Size: $PACKAGE_SIZE"
        echo ""
        echo "The package contains:"
        echo "- Application executable and dependencies"
        echo "- Service management scripts (Start/Stop/Uninstall)"
        echo "- Deployment instructions (README.txt)"
        echo ""
        echo "Ready for deployment to other machines!"
    else
        echo "ERROR: Failed to create ZIP package"
        exit 1
    fi
else
    echo ""
    echo "ZIP utility not available. Package files are ready in: $TEMP_DIR"
    echo "Please manually create ZIP from the $TEMP_DIR folder"
    echo "or use a ZIP utility to compress the $TEMP_DIR folder"
    echo "into ${PACKAGE_NAME}.zip"
    echo ""
    echo "Package contents:"
    ls -la "$TEMP_DIR"
    exit 0
fi

# Clean up temporary directory
echo ""
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo ""
echo "Deployment package creation completed!"
