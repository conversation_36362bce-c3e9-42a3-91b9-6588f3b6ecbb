LSB Telemetry Service - Deployment Package
==========================================

QUICK START:
1. Extract all files to a folder (e.g., C:\LSB\TelemetryService\)
2. Right-click StartService.bat -> "Run as administrator"
3. Done!

SERVICE MANAGEMENT:
- Install/Start: StartService.bat (run as admin)
- Stop: StopService.bat (run as admin)  
- Uninstall: UninstallService.bat (run as admin)

REQUIREMENTS:
- Windows 10/11 or Windows Server 2016+
- Administrator privileges
- .NET 8.0 Runtime (auto-installed if missing)

CONFIGURATION:
- Edit appsettings.json for Discord channels and monitoring settings
- Create C:\ProgramData\LSB\logmonitor.config for client settings

TROUBLESHOOTING:
- Check Windows Event Viewer if service fails to start
- Ensure all files are in the same folder
- Run scripts as Administrator

Generated: June 2025
