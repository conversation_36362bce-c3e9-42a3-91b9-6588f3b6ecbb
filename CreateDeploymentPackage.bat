@echo off
echo ========================================
echo LSB Telemetry Service - Package Creator
echo ========================================
echo.

REM Set variables
set PACKAGE_NAME=LSB.Telemetry.Service.Deployment
set TEMP_DIR=TempPackage
set BIN_PATH=LSB.LogMonitor.Service\bin\Release\net8.0
set SCRIPTS_PATH=LSB.LogMonitor.Service\Scripts

REM Check if Release build exists
if not exist "%BIN_PATH%" (
    echo ERROR: Release build not found at: %BIN_PATH%
    echo Please build the project in Release mode first.
    pause
    exit /b 1
)

REM Check if Scripts directory exists
if not exist "%SCRIPTS_PATH%" (
    echo ERROR: Scripts directory not found at: %SCRIPTS_PATH%
    pause
    exit /b 1
)

REM Clean up any existing temp directory
if exist "%TEMP_DIR%" (
    echo Cleaning up existing temp directory...
    rmdir /s /q "%TEMP_DIR%"
)

REM Create temporary package structure
echo Creating package structure...
mkdir "%TEMP_DIR%"
mkdir "%TEMP_DIR%\App"
mkdir "%TEMP_DIR%\Scripts"

REM Copy application files (excluding PDB files)
echo Copying application files...
xcopy "%BIN_PATH%\*" "%TEMP_DIR%\App\" /E /I /Y /Q
del "%TEMP_DIR%\App\*.pdb" 2>nul

REM Copy utility scripts
echo Copying utility scripts...
copy "%SCRIPTS_PATH%\StartService.bat" "%TEMP_DIR%\Scripts\" >nul
copy "%SCRIPTS_PATH%\StopService.bat" "%TEMP_DIR%\Scripts\" >nul
copy "%SCRIPTS_PATH%\UninstallService.bat" "%TEMP_DIR%\Scripts\" >nul

REM Create README file
echo Creating deployment README...
(
echo # LSB Telemetry Service - Deployment Package
echo.
echo ## System Requirements
echo - Windows 10/11 or Windows Server 2016+
echo - .NET 8.0 Runtime ^(will be installed automatically if missing^)
echo - Administrator privileges for service installation
echo.
echo ## Installation Instructions
echo.
echo 1. Extract this ZIP package to a folder on the target machine
echo    Example: C:\Program Files\LSB\TelemetryService\
echo.
echo 2. Open Command Prompt or PowerShell as Administrator
echo.
echo 3. Navigate to the Scripts folder:
echo    cd "C:\Program Files\LSB\TelemetryService\Scripts"
echo.
echo 4. Run the installation script:
echo    StartService.bat
echo.
echo ## Service Management
echo.
echo ### Start/Install Service
echo - Run: Scripts\StartService.bat
echo - This script will:
echo   - Check if service is already installed
echo   - Install the service if not present
echo   - Start the service
echo   - Configure automatic startup
echo.
echo ### Stop Service
echo - Run: Scripts\StopService.bat
echo - This will stop the running service
echo.
echo ### Uninstall Service
echo - Run: Scripts\UninstallService.bat
echo - This will stop and completely remove the service
echo.
echo ## Configuration
echo.
echo The service uses configuration files located in:
echo - App\appsettings.json ^(main configuration^)
echo - C:\ProgramData\LSB\logmonitor.config ^(client-specific settings^)
echo.
echo Make sure to configure these files according to your environment before starting the service.
echo.
echo ## Troubleshooting
echo.
echo 1. If service fails to start:
echo    - Check Windows Event Viewer ^(Windows Logs ^> Application^)
echo    - Verify configuration files are valid JSON
echo    - Ensure all required directories exist
echo.
echo 2. If installation fails:
echo    - Verify you're running as Administrator
echo    - Check if service name conflicts with existing services
echo    - Ensure .NET 8.0 Runtime is installed
echo.
echo 3. For support:
echo    - Check service logs in Windows Event Viewer
echo    - Review configuration files for syntax errors
echo.
echo ## Package Contents
echo.
echo - App\                     - Main application files
echo - Scripts\                 - Service management scripts
echo - README.txt              - This file
echo.
echo Generated on: %DATE% %TIME%
) > "%TEMP_DIR%\README.txt"

REM Create ZIP using PowerShell if available, otherwise use tar
echo Creating ZIP package...
powershell -Command "if (Test-Path '%PACKAGE_NAME%.zip') { Remove-Item '%PACKAGE_NAME%.zip' -Force }; Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::CreateFromDirectory('%TEMP_DIR%', '%PACKAGE_NAME%.zip')" 2>nul

if exist "%PACKAGE_NAME%.zip" (
    echo.
    echo ========================================
    echo PACKAGE CREATED SUCCESSFULLY!
    echo ========================================
    echo Package: %PACKAGE_NAME%.zip
    echo.
    echo The package contains:
    echo - Application executable and dependencies
    echo - Service management scripts ^(Start/Stop/Uninstall^)
    echo - Deployment instructions ^(README.txt^)
    echo.
    echo Ready for deployment to other machines!
) else (
    echo.
    echo PowerShell not available, trying alternative method...
    echo Please manually create ZIP from the TempPackage folder
    echo or use a ZIP utility to compress the TempPackage folder
    echo into %PACKAGE_NAME%.zip
)

REM Clean up temporary directory
echo.
echo Cleaning up temporary files...
rmdir /s /q "%TEMP_DIR%"

echo.
pause
