# LSB Telemetry Service - Deployment Instructions

## Package Created
✅ **LSB.Telemetry.Service.Deployment.zip** (1.7 MB)

## What's Included in the Package

### 📁 App/ Directory
- **LSB.LogMonitor.Service.exe** - Main service executable
- **All required DLL dependencies** - .NET runtime libraries and dependencies
- **appsettings.json** - Main configuration file
- **Runtime files** - .NET 8.0 runtime configuration

### 📁 Scripts/ Directory
- **StartService.bat** - Install and start the service
- **StopService.bat** - Stop the running service  
- **UninstallService.bat** - Completely remove the service

### 📄 Documentation
- **README.txt** - Complete deployment and troubleshooting guide

## Quick Deployment Steps

### 1. Transfer Package
Copy `LSB.Telemetry.Service.Deployment.zip` to the target machine

### 2. Extract Package
Extract to a permanent location (e.g., `C:\Program Files\LSB\TelemetryService\`)

### 3. Install & Start Service
1. Open Command Prompt as **Administrator**
2. Navigate to the Scripts folder
3. Run: `StartService.bat`

### 4. Verify Installation
- Check Windows Services (services.msc) for "LSB Telemetry Service"
- Check Windows Event Viewer for any startup errors

## Service Management Commands

```batch
# Install and start service
Scripts\StartService.bat

# Stop service
Scripts\StopService.bat

# Uninstall service completely
Scripts\UninstallService.bat
```

## Configuration Requirements

### Before Starting the Service:
1. **Configure appsettings.json** in the App folder
2. **Create logmonitor.config** at `C:\ProgramData\LSB\logmonitor.config`
3. **Ensure log directories exist** as specified in configuration

### Key Configuration Files:
- `App\appsettings.json` - Discord channels, monitoring settings
- `C:\ProgramData\LSB\logmonitor.config` - Client-specific account settings

## System Requirements
- Windows 10/11 or Windows Server 2016+
- .NET 8.0 Runtime (auto-installed if missing)
- Administrator privileges for service installation

## Troubleshooting
- **Service won't start**: Check Event Viewer → Windows Logs → Application
- **Configuration errors**: Validate JSON syntax in config files
- **Permission issues**: Ensure running as Administrator

## Package Features
✅ Self-contained deployment (all dependencies included)  
✅ Automatic service installation and configuration  
✅ Failure recovery (service restarts on crash)  
✅ Automatic startup with Windows  
✅ Complete uninstall capability  

---
**Generated**: $(date)  
**Package Size**: 1.7 MB  
**Ready for deployment to any Windows machine!**
